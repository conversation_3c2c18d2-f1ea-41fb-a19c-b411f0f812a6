{"buildFiles": ["D:\\droidrun-dag\\high-performance-droidrun\\app\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\droidrun-dag\\high-performance-droidrun\\app\\.cxx\\RelWithDebInfo\\513pl5v4\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\droidrun-dag\\high-performance-droidrun\\app\\.cxx\\RelWithDebInfo\\513pl5v4\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"droidrun_native::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "droidrun_native", "output": "D:\\droidrun-dag\\high-performance-droidrun\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\513pl5v4\\obj\\x86_64\\libdroidrun_native.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}