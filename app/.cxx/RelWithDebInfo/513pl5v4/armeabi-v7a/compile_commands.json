[{"directory": "D:/droidrun-dag/high-performance-droidrun/app/.cxx/RelWithDebInfo/513pl5v4/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID_PLATFORM -DLOG_TAG=\\\"DroidRunNative\\\" -Ddroidrun_native_EXPORTS -ID:/droidrun-dag/high-performance-droidrun/app/src/main/cpp -ID:/droidrun-dag/high-performance-droidrun/app/src/main/cpp/llm -ID:/droidrun-dag/high-performance-droidrun/app/src/main/cpp/jni -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -mfpu=neon -O2 -g -DNDEBUG -fPIC -std=c++17 -o CMakeFiles\\droidrun_native.dir\\llm\\inference_engine.cpp.o -c D:\\droidrun-dag\\high-performance-droidrun\\app\\src\\main\\cpp\\llm\\inference_engine.cpp", "file": "D:\\droidrun-dag\\high-performance-droidrun\\app\\src\\main\\cpp\\llm\\inference_engine.cpp"}, {"directory": "D:/droidrun-dag/high-performance-droidrun/app/.cxx/RelWithDebInfo/513pl5v4/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DANDROID_PLATFORM -DLOG_TAG=\\\"DroidRunNative\\\" -Ddroidrun_native_EXPORTS -ID:/droidrun-dag/high-performance-droidrun/app/src/main/cpp -ID:/droidrun-dag/high-performance-droidrun/app/src/main/cpp/llm -ID:/droidrun-dag/high-performance-droidrun/app/src/main/cpp/jni -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -std=c++17 -mfpu=neon -O2 -g -DNDEBUG -fPIC -std=c++17 -o CMakeFiles\\droidrun_native.dir\\jni\\droidrun_jni.cpp.o -c D:\\droidrun-dag\\high-performance-droidrun\\app\\src\\main\\cpp\\jni\\droidrun_jni.cpp", "file": "D:\\droidrun-dag\\high-performance-droidrun\\app\\src\\main\\cpp\\jni\\droidrun_jni.cpp"}]