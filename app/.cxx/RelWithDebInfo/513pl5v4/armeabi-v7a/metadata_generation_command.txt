                        -HD:\droidrun-dag\high-performance-droidrun\app\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.1.8937393\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-std=c++17
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\droidrun-dag\high-performance-droidrun\app\build\intermediates\cxx\RelWithDebInfo\513pl5v4\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\droidrun-dag\high-performance-droidrun\app\build\intermediates\cxx\RelWithDebInfo\513pl5v4\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\droidrun-dag\high-performance-droidrun\app\.cxx\RelWithDebInfo\513pl5v4\armeabi-v7a
-GNinja
-DANDROID_STL=c++_shared
-DCMAKE_CXX_FLAGS_INIT=-DANDROID_PATH_FIX
                        Build command args: []
                        Version: 2