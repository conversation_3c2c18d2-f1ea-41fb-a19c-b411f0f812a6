// Minimal build configuration
buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url = uri("https://jitpack.io") }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.3.1'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.20'
        classpath 'org.jetbrains.kotlin:kotlin-serialization:1.9.20'
        classpath 'com.squareup:javapoet:1.13.0'
    }
}

// 全局依赖版本强制，避免旧版 JavaPoet 冲突
configurations.all {
    resolutionStrategy {
        force 'com.squareup:javapoet:1.13.0'
    }
}

// 移除allprojects块，因为仓库已在settings.gradle中定义

task clean(type: Delete) {
    delete rootProject.buildDir
}
