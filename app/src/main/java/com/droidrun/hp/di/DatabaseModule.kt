package com.droidrun.hp.di

import android.content.Context
import androidx.room.Room
import com.droidrun.hp.data.database.DroidRunDatabase
import com.droidrun.hp.data.dao.CacheDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDroidRunDatabase(@ApplicationContext context: Context): DroidRunDatabase {
        return Room.databaseBuilder(
            context,
            DroidRunDatabase::class.java,
            "droidrun_database"
        )
        .fallbackToDestructiveMigration()
        .build()
    }
    
    @Provides
    @Singleton
    fun provideCacheDao(database: DroidRunDatabase): CacheDao {
        return database.cacheDao()
    }

    @Provides
    @Singleton
    fun provideTaskDao(database: DroidRunDatabase): com.droidrun.hp.data.database.TaskDao {
        return database.taskDao()
    }

    @Provides
    @Singleton
    fun provideActionHistoryDao(database: DroidRunDatabase): com.droidrun.hp.data.database.ActionHistoryDao {
        return database.actionHistoryDao()
    }
}
