package com.droidrun.hp.data.database

import androidx.room.TypeConverter
import com.droidrun.hp.data.model.TaskStatus
import com.droidrun.hp.data.model.UIAction
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Room数据库类型转换器
 */
class Converters {
    private val gson = Gson()
    
    @TypeConverter
    fun fromStringList(value: List<String>?): String? {
        return if (value == null) null else gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringList(value: String?): List<String>? {
        if (value == null) return null
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(value, listType)
    }
    
    @TypeConverter
    fun fromStringMap(value: Map<String, String>?): String? {
        return if (value == null) null else gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringMap(value: String?): Map<String, String>? {
        if (value == null) return null
        val mapType = object : TypeToken<Map<String, String>>() {}.type
        return gson.fromJson(value, mapType)
    }
    
    @TypeConverter
    fun fromUIAction(value: UIAction?): String? {
        return if (value == null) null else gson.toJson(value)
    }
    
    @TypeConverter
    fun toUIAction(value: String?): UIAction? {
        return if (value == null) null else gson.fromJson(value, UIAction::class.java)
    }
    
    @TypeConverter
    fun fromTaskStatus(value: TaskStatus?): String? {
        return value?.name
    }
    
    @TypeConverter
    fun toTaskStatus(value: String?): TaskStatus? {
        return if (value == null) null else TaskStatus.valueOf(value)
    }
} 