{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "droidrun_native", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "droidrun_native::@6890427a1f51a3e7e1df", "jsonFile": "target-droidrun_native-RelWithDebInfo-85323a0d3483fc941ade.json", "name": "droidrun_native", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/droidrun-dag/high-performance-droidrun/app/.cxx/RelWithDebInfo/513pl5v4/x86_64", "source": "D:/droidrun-dag/high-performance-droidrun/app/src/main/cpp"}, "version": {"major": 2, "minor": 3}}