{"artifacts": [{"path": "D:/droidrun-dag/high-performance-droidrun/app/build/intermediates/cxx/RelWithDebInfo/513pl5v4/obj/x86_64/libdroidrun_native.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 46, "parent": 0}, {"command": 1, "file": 0, "line": 54, "parent": 0}, {"command": 2, "file": 0, "line": 62, "parent": 0}, {"command": 3, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -O2 -g -DNDEBUG -fPIC"}, {"fragment": "-std=c++17"}], "defines": [{"backtrace": 3, "define": "ANDROID_PLATFORM"}, {"backtrace": 3, "define": "LOG_TAG=\"DroidRunNative\""}, {"define": "droidrun_native_EXPORTS"}], "includes": [{"backtrace": 4, "path": "D:/droidrun-dag/high-performance-droidrun/app/src/main/cpp"}, {"backtrace": 4, "path": "D:/droidrun-dag/high-performance-droidrun/app/src/main/cpp/llm"}, {"backtrace": 4, "path": "D:/droidrun-dag/high-performance-droidrun/app/src/main/cpp/jni"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "droidrun_native::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\24\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\24\\libandroid.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\24\\libjnigraphics.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "droidrun_native", "nameOnDisk": "libdroidrun_native.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "llm/inference_engine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "jni/droidrun_jni.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}