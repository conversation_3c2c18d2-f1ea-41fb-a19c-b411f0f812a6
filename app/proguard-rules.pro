# ProGuard / R8 rules for DroidRun HP

# 保留 ViewBinding 生成类，避免 NoClassDefFoundError
-keep class **Binding { *; }
-keep class com.droidrun.hp.databinding.** { *; }

# 保留 Hilt 生成代码 (Hilt 已添加默认keep，但加固)
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.internal.GeneratedComponent { *; }

# 保留 Room 实体及 Dao（含泛型签名）
-keep class androidx.room.** { *; }
-keepclassmembers class * {
    @androidx.room.* <fields>;
    @androidx.room.* <methods>;
}

# 保留 Kotlin 协程内部类，防止混淆导致 StateMachine 找不到
-keepclassmembers class kotlinx.coroutines.internal.MainDispatcherFactory { *; }

# 保留 Fragment 类避免被移除（可选）
-keep class com.droidrun.hp.ui.**Fragment { *; }

# 跳过已处理 