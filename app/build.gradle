plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.devtools.ksp' version '1.9.20-1.0.14'
    id 'kotlin-parcelize'
    id 'com.google.dagger.hilt.android' version '2.48'
    id 'kotlinx-serialization'
}

// JDK模块配置不再需要，KSP不依赖javac
// allprojects {
//    tasks.withType(JavaCompile) {
//        options.compilerArgs += [
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED',
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED',
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED',
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED',
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED',
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED',
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED',
//            '--add-exports', 'jdk.compiler/com.sun.tools.javac.jvm=ALL-UNNAMED'
//        ]
//    }
// }

// 全局属性设置
ext {
    kotlinJvmTarget = "17"
}

android {
    namespace 'com.droidrun.hp'
    compileSdk 34

    defaultConfig {
        applicationId "com.droidrun.hp"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // 设置CMake命令行参数
        externalNativeBuild {
            cmake {
                cppFlags '-std=c++17'
                arguments "-DANDROID_STL=c++_shared"
                arguments "-DCMAKE_CXX_FLAGS_INIT=-DANDROID_PATH_FIX"
            }
        }
        
        // 指定支持的ABI
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }
    
    // 配置CMake构建
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        viewBinding true
        buildConfig true
    }
    
    // 配置资源目录
    sourceSets {
        main {
            assets.srcDirs = ['src/main/assets']
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
    }
}

// 强制所有kotlin编译任务使用JVM 17
tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
    kotlinOptions {
        jvmTarget = "17"
    }
}

// KSP配置替代KAPT
ksp {
    arg("kotlinJvmTarget", "17")
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'
    
    // Network libraries for external API calls
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // JSON processing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Logging framework
    implementation 'com.jakewharton.timber:timber:5.0.1'
    
    // Dependency injection - Hilt with KSP
    implementation 'com.google.dagger:hilt-android:2.48'
    ksp 'com.google.dagger:hilt-compiler:2.48'
    
    // Kotlin serialization
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0'
    
    // HTTP server
    implementation 'org.nanohttpd:nanohttpd:2.3.1'
    
    // Preferences
    implementation 'androidx.preference:preference-ktx:1.2.1'
    
    // Room database with KSP
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    ksp 'androidx.room:room-compiler:2.6.1'
    
    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // DataStore - 添加缺少的DataStore依赖
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    implementation 'androidx.datastore:datastore:1.0.0'
    
    // Android Tracing API - 用于性能分析
    implementation 'androidx.tracing:tracing:1.1.0'
    implementation 'androidx.tracing:tracing-ktx:1.1.0'
    
    // ViewBinding Delegate - 简化ViewBinding使用
    implementation 'com.github.kirich1409:viewbindingpropertydelegate-noreflection:1.5.9'
    
    // LRU Cache - 用于缓存实现
    implementation 'androidx.collection:collection-ktx:1.3.0'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    
    // ---------- 单元测试依赖 ----------
    testImplementation 'io.mockk:mockk:1.13.9'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
}
